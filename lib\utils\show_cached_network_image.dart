import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:shimmer/shimmer.dart';

class ShowCachedNetworkImage extends StatefulWidget {
  const ShowCachedNetworkImage({
    super.key,
    required this.imageurl,
    this.fit,
    this.height,
    this.width,
    this.borderRadius,
    this.errorWidget,
  });

  final String imageurl;
  final double? height;
  final BoxFit? fit;
  final double? width;
  final BorderRadius? borderRadius;
  final Widget? errorWidget;

  @override
  _ShowCachedNetworkImageState createState() => _ShowCachedNetworkImageState();
}

class _ShowCachedNetworkImageState extends State<ShowCachedNetworkImage> {
  late String currentImageUrl;
  String? _lastWidgetImageUrl;

  @override
  void initState() {
    super.initState();
    currentImageUrl = widget.imageurl;
    _lastWidgetImageUrl = widget.imageurl;
  }

  @override
  void didUpdateWidget(ShowCachedNetworkImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Check if the imageurl prop has changed
    if (oldWidget.imageurl != widget.imageurl) {
      setState(() {
        // Use the new URL directly without cache busting to preserve caching
        currentImageUrl = widget.imageurl;
        _lastWidgetImageUrl = widget.imageurl;
      });
    }
  }

  void reloadImage() {
    setState(() {
      // Only add timestamp when explicitly reloading (user action)
      currentImageUrl =
          '${widget.imageurl}?reload=${DateTime.now().millisecondsSinceEpoch}';
    });
  }

  @override
  Widget build(BuildContext context) {
    if (currentImageUrl.isEmpty || !Uri.parse(currentImageUrl).isAbsolute) {
      return widget.errorWidget ??
          Container(
            color: Colors.grey,
            child: const Center(
              child: Text(
                'Invalid image URL',
                style: TextStyle(color: Colors.white),
              ),
            ),
          );
    }

    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.circular(4.5),
      child: FastCachedImage(
        url: currentImageUrl,
        fit: widget.fit ?? BoxFit.cover,
        height: widget.height ?? 25,
        width: widget.width ?? 35,
        fadeInDuration: const Duration(milliseconds: 300), // Faster fade for better UX
        errorBuilder: (context, url, error) {
          return widget.errorWidget ??
              Image.network(
                height: 200.h,
                width: 390.w,
                AssetUrl.onekittyBannnerUrl,
              );
        },
        loadingBuilder: (context, url) {
          return Container(
            width: widget.width ?? 200,
            height: widget.height ?? 120,
            color: Colors.grey.shade100,
            child: Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.grey.shade400,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

// Cache for image dimensions to avoid recalculating layout
final Map<String, Size> _imageSizeCache = <String, Size>{};

class ShowCachedNetworkImage extends StatefulWidget {
  const ShowCachedNetworkImage({
    super.key,
    required this.imageUrl,
    this.initialHeight,
    this.initialWidth,
    this.borderRadius = 4.5,
  });

  final String imageUrl;
  final double? initialHeight;
  final double? initialWidth;
  final double borderRadius;

  @override
  _ShowCachedNetworkImageState createState() =>
      _ShowCachedNetworkImageState();
}

class _ShowCachedNetworkImageState
    extends State<ShowCachedNetworkImage> with AutomaticKeepAliveClientMixin {
  // Cache computed layout values
  Size? _cachedImageSize;
  double? _cachedWidth;
  double? _cachedHeight;
  BoxFit? _cachedFit;
  bool _layoutComputed = false;

  @override
  bool get wantKeepAlive => true; // Keep widget alive to maintain cached values

  @override
  void initState() {
    super.initState();
    _loadCachedImageSize();
  }

  @override
  void didUpdateWidget(ShowCachedNetworkImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Only recompute if URL changed
    if (oldWidget.imageUrl != widget.imageUrl) {
      _layoutComputed = false;
      _loadCachedImageSize();
    }
  }

  void _loadCachedImageSize() {
    // Check if we have cached size for this image
    if (_imageSizeCache.containsKey(widget.imageUrl)) {
      _cachedImageSize = _imageSizeCache[widget.imageUrl];
      _layoutComputed = false; // Will recompute layout with cached size
    }
  }



  void _computeLayout(BoxConstraints constraints) {
    if (_layoutComputed) return;

    double? height = widget.initialHeight;
    double? width = widget.initialWidth;
    BoxFit fit = BoxFit.contain;

    if (_cachedImageSize != null) {
      final double aspectRatio = _cachedImageSize!.width / _cachedImageSize!.height;
      if (aspectRatio > 1) {
        // Landscape
        width = constraints.maxWidth;
        height = width / aspectRatio;
        fit = BoxFit.fitWidth;
      } else {
        // Portrait
        height = constraints.maxHeight.isFinite
            ? constraints.maxHeight
            : 300; // Default height if constraint is infinite
        width = height * aspectRatio;
        fit = BoxFit.fitHeight;
      }
    } else {
      // If image info is not available, use default or constrained sizes
      width = width ?? constraints.maxWidth;
      height = height ??
          (constraints.maxHeight.isFinite
              ? constraints.maxHeight
              : 300); // Default height if constraint is infinite
    }

    // Ensure height and width are finite and within constraints
    height = height.clamp(
        0.0,
        constraints.maxHeight.isFinite
            ? constraints.maxHeight
            : double.infinity);
    width = width.clamp(0.0, constraints.maxWidth);

    _cachedWidth = width;
    _cachedHeight = height;
    _cachedFit = fit;
    _layoutComputed = true;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return LayoutBuilder(
      builder: (context, constraints) {
        // Compute layout only if needed
        _computeLayout(constraints);

        return SizedBox(
          width: _cachedWidth,
          height: _cachedHeight,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(widget.borderRadius),
            child: FastCachedImage(
              url: widget.imageUrl,
              fit: _cachedFit ?? BoxFit.contain,
              fadeInDuration: const Duration(milliseconds: 200), // Slightly faster fade
              errorBuilder: (context, url, error) {
                return _buildErrorWidget();
              },
              loadingBuilder: (context, url) {
                return _buildLoadingWidget();
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Image.network(
          height: 200.h,
          width: 390.w,
          AssetUrl.onekittyBannnerUrl,
        ),
        const Positioned(
          top: 8,
          right: 8,
          child: Tooltip(
            triggerMode: TooltipTriggerMode.tap,
            message: 'Error loading event image',
            child: Icon(Icons.error),
          ),
        )
      ],
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(widget.borderRadius),
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(widget.borderRadius),
          ),
        ),
      ),
    );
  }
}
